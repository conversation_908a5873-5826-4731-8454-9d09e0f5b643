#!/usr/bin/env python3
"""
Code Developer Web UI 启动脚本
避免复杂的配置依赖，直接启动界面
"""

import argparse
import asyncio
import json
import os
import time
import traceback
from typing import Any, Dict, List, Optional, Tuple
import re

import gradio as gr
import httpx


class SimpleCodeDeveloperUI:
    """简化版Code Developer UI类"""

    def __init__(self, api_base_url: str = None):
        """初始化Simple Code Developer UI"""
        if api_base_url and api_base_url.endswith("/"):
            api_base_url = api_base_url[:-1]
        self.api_base_url = api_base_url or "http://localhost:8000"
        
        # 配置
        self.message_timeout = 300.0  # 5分钟超时
        
        # 状态跟踪
        self.session_info = {
            "session_id": None,
            "server_status": "Not Started",
            "server_url": None,
            "server_port": None
        }
        
        # 主题和界面
        self.theme = self._create_theme()
        self.interface = self._build_interface()

    def _create_theme(self) -> gr.Theme:
        """创建Code Developer专用主题"""
        return gr.Theme(
            primary_hue="green",
            secondary_hue="emerald", 
            neutral_hue="slate",
        )

    async def _send_message_to_code_developer(
        self, message: str, history: List[Dict[str, Any]], session_id: str = None
    ) -> Tuple[List[Dict[str, Any]], str, Dict[str, Any]]:
        """发送消息到code_developer应用"""
        if not message:
            return history, session_id or "", self.session_info

        new_history = history + [
            {"role": "user", "content": message},
            {"role": "assistant", "content": None},
        ]
        
        progress = gr.Progress(track_tqdm=False)
        progress(0, desc="初始化代码开发请求...")

        try:
            url = f"{self.api_base_url}/apps/code_developer/message"
            timeout = httpx.Timeout(self.message_timeout)
            print(f"发送消息到Code Developer: {url}")
            
            context = {}
            if session_id:
                context["session_id"] = session_id
            
            start_time = time.time()
            progress(0.1, desc="正在处理代码开发请求...")
            
            async with httpx.AsyncClient(timeout=timeout) as client:
                try:
                    response = await client.post(
                        url, 
                        json={"message": message, "requestId": "code_dev", "context": context}
                    )
                    
                    progress(0.9, desc="处理响应...")
                    
                    if response.status_code != 200:
                        error_msg = f"请求失败 (状态码: {response.status_code}): {response.text}"
                        print(f"错误: {error_msg}")
                        new_history[-1]["content"] = f"❌ **请求错误**\n\n{error_msg}"
                        return new_history, session_id, self.session_info
                    
                    data = response.json()
                    ai_response = data.get("response", "")
                    returned_session_id = data.get("session_id", session_id)
                    
                    # 解析响应中的服务器信息
                    self._parse_server_info(ai_response, returned_session_id)
                    
                    progress(1.0, desc="完成")
                    new_history[-1]["content"] = ai_response
                    return new_history, returned_session_id, self.session_info
                    
                except Exception as e:
                    print(f"请求异常: {e}")
                    new_history[-1]["content"] = f"❌ **连接错误**\n\n无法连接到API服务器: {str(e)}\n\n请确保API服务器正在运行。"
                    return new_history, session_id or "", self.session_info
                        
        except Exception as e:
            print(f"代码开发过程中发生错误: {e}")
            error_message = f"""❌ **系统错误**

代码开发过程中出现问题: {str(e)}

请检查网络连接或联系管理员。
"""
            new_history[-1]["content"] = error_message
            return new_history, session_id or "", self.session_info

    def _parse_server_info(self, response: str, session_id: str):
        """解析AI响应中的服务器信息"""
        # 更新session信息
        if session_id and session_id != self.session_info.get("session_id"):
            self.session_info["session_id"] = session_id
        
        # 查找服务器URL信息
        url_patterns = [
            r'🔗\s*\*\*URL\*\*:\s*(https?://[^\s\n]+)',  # 匹配格式化的URL
            r'URL[：:]\s*(https?://[^\s\n]+)',            # 匹配URL:格式
            r'访问链接[：:]\s*(https?://[^\s\n]+)',        # 匹配访问链接
            r'(https?://[^\s\n]*:\d+)',                   # 匹配任何带端口的URL
        ]
        
        for pattern in url_patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                url = match.group(1)
                self.session_info["server_url"] = url
                # 提取端口号
                port_match = re.search(r':(\d+)', url)
                if port_match:
                    self.session_info["server_port"] = port_match.group(1)
                break
        
        # 检查服务器状态
        if "Server Successfully Deployed" in response or "服务器启动成功" in response:
            self.session_info["server_status"] = "Running"
        elif "创建AgentBay会话" in response or "Successfully created" in response:
            self.session_info["server_status"] = "Session Created"
        elif "Failed" in response or "失败" in response:
            self.session_info["server_status"] = "Failed"

    def _format_server_status(self) -> str:
        """格式化服务器状态显示"""
        status = self.session_info["server_status"]
        url = self.session_info.get("server_url")
        port = self.session_info.get("server_port")
        session_id = self.session_info.get("session_id")
        
        status_emoji = {
            "Not Started": "⚪",
            "Session Created": "🟡", 
            "Running": "🟢",
            "Failed": "🔴"
        }
        
        status_display = f"{status_emoji.get(status, '⚪')} **状态**: {status}"
        
        if session_id:
            status_display += f"\n📋 **会话ID**: `{session_id[:8]}...`"
        
        if url:
            status_display += f"\n🔗 **访问地址**: [{url}]({url})"
            
        if port:
            status_display += f"\n🔧 **端口**: {port}"
            
        return status_display

    def _build_interface(self) -> gr.Blocks:
        """构建Code Developer专用界面"""
        with gr.Blocks(
            title="Code Developer - 代码开发助手",
            theme=self.theme,
            css="""
            #header-area {
                text-align: center;
                margin-bottom: 20px;
                background: linear-gradient(90deg, #10b981, #059669);
                color: white;
                padding: 20px;
                border-radius: 10px;
            }
            #header-area h1 {
                margin: 0;
                font-size: 2.5em;
            }
            #header-area p {
                margin: 10px 0 0 0;
                opacity: 0.9;
                font-size: 1.1em;
            }
            .status-panel {
                background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
                border: 1px solid #10b981;
                border-radius: 8px;
                padding: 15px;
            }
            .server-link {
                background: #10b981;
                color: white !important;
                padding: 10px 20px;
                border-radius: 6px;
                text-decoration: none !important;
                display: inline-block;
                font-weight: bold;
                margin: 10px 0;
            }
            .server-link:hover {
                background: #059669;
            }
            .chatbot-container {
                border: 2px solid #10b981;
                border-radius: 10px;
                background: white;
            }
            """
        ) as interface:
            
            # 页面标题区域
            with gr.Column(elem_id="header-area"):
                gr.Markdown("""# 🚀 Code Developer
                **专业的Python代码开发和云端部署助手** | 基于AgentBay云环境""")
            
            # 隐藏状态
            session_id_state = gr.State(value="")
            
            with gr.Row():
                # 左侧面板 - 项目信息和状态
                with gr.Column(scale=1):
                    with gr.Group(elem_classes="status-panel"):
                        gr.Markdown("## 📊 项目状态")
                        server_status = gr.Markdown("⚪ **状态**: 未开始")
                        
                        # 快捷操作按钮
                        with gr.Row():
                            create_session_btn = gr.Button("🏗️ 创建环境", variant="secondary", size="sm")
                            deploy_btn = gr.Button("🚀 快速部署", variant="primary", size="sm")
                        
                        with gr.Row():
                            check_status_btn = gr.Button("📊 检查状态", variant="secondary", size="sm")
                            get_link_btn = gr.Button("🔗 获取链接", variant="secondary", size="sm")
                    
                    # API配置信息
                    with gr.Accordion("🔧 连接信息", open=False):
                        gr.Markdown(f"**API服务器**: {self.api_base_url}")
                        gr.Markdown(f"**超时设置**: {self.message_timeout} 秒")
                        gr.Markdown("**状态**: 连接正常" if True else "**状态**: 连接异常")
                
                # 右侧主面板 - 对话界面
                with gr.Column(scale=2, elem_classes="chatbot-container"):
                    # 聊天机器人界面
                    chatbot = gr.Chatbot(
                        height=600,
                        render_markdown=True,
                        show_copy_button=True,
                        type="messages",
                        elem_classes="chatbot",
                        sanitize_html=False,
                        show_label=False
                    )
                    
                    # 消息输入区域
                    with gr.Row():
                        message_box = gr.Textbox(
                            placeholder="输入你的代码需求，比如：'创建一个Flask Web应用，包含用户注册和登录功能'",
                            label="",
                            lines=3,
                            scale=10,
                            container=False,
                            show_label=False
                        )
                        send_btn = gr.Button("💬 发送", variant="primary", scale=1)
                    
                    # 操作按钮
                    with gr.Row():
                        clear_btn = gr.Button("🧹 清空对话", variant="secondary")

            # 页脚
            with gr.Row():
                gr.Markdown("""
                ---
                💡 **使用提示**: 
                - 描述你需要的应用类型和功能
                - 系统会自动创建云环境并部署代码  
                - 部署成功后会提供访问链接
                
                🔧 **技术栈**: Python + AgentBay云环境 + 自动化部署
                """)

            # 事件绑定
            def update_server_status():
                return self._format_server_status()

            # 发送消息事件
            async def send_and_clear(message, history, session_id):
                if not message.strip():
                    return history, "", session_id, update_server_status()
                
                new_history, new_session_id, session_info = await self._send_message_to_code_developer(
                    message, history, session_id
                )
                return new_history, "", new_session_id, update_server_status()

            # 快捷操作事件
            def quick_create_session():
                return "请创建一个AgentBay代码开发环境"

            def quick_deploy():
                return "请使用Flask框架创建一个简单的Web应用，包含主页和健康检查接口，然后部署并获取访问链接"

            def quick_check_status():
                return "请检查当前AgentBay会话状态和运行中的进程"

            def quick_get_link():
                return "请获取当前运行服务的访问链接"

            # 清空对话
            def clear_conversation():
                self.session_info = {
                    "session_id": None,
                    "server_status": "Not Started",
                    "server_url": None,
                    "server_port": None
                }
                return [], "", update_server_status()

            # 绑定事件
            msg_events = [message_box.submit, send_btn.click]
            for event in msg_events:
                event(
                    send_and_clear,
                    inputs=[message_box, chatbot, session_id_state],
                    outputs=[chatbot, message_box, session_id_state, server_status]
                )

            # 快捷按钮事件
            create_session_btn.click(quick_create_session, outputs=[message_box])
            deploy_btn.click(quick_deploy, outputs=[message_box])
            check_status_btn.click(quick_check_status, outputs=[message_box])
            get_link_btn.click(quick_get_link, outputs=[message_box])

            # 清空对话事件  
            clear_btn.click(clear_conversation, outputs=[chatbot, session_id_state, server_status])

            # 欢迎消息
            def load_welcome_message():
                return [
                    {"role": "assistant", "content": """🎉 **欢迎使用 Code Developer！**

我是你的专业代码开发助手，专门帮你创建、部署和管理Python应用。

**我可以帮你：**
- 🏗️ 创建各种Python应用（Web、API、数据处理等）
- ☁️ 自动部署到AgentBay云环境  
- 🔗 提供应用访问链接
- 📦 管理项目依赖和配置
- 🔍 监控应用运行状态

**快速开始：**
1. 点击左侧快捷按钮开始
2. 描述你需要的应用功能
3. 我会为你创建完整的应用并部署上线！

现在，告诉我你想要创建什么应用吧！ 🚀"""}
                ]
            
            interface.load(
                load_welcome_message,
                outputs=[chatbot]
            )

        return interface

    def launch(self, **kwargs):
        """启动界面"""
        return self.interface.launch(**kwargs)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Simple Code Developer Web UI")
    parser.add_argument("--host", default="127.0.0.1", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=7861, help="服务器端口")
    parser.add_argument("--api-url", default="http://localhost:8000", help="API服务器地址")
    parser.add_argument("--share", action="store_true", help="创建公共分享链接")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    
    args = parser.parse_args()
    
    try:
        print("🚀 启动 Simple Code Developer Web UI")
        print(f"   主机: {args.host}:{args.port}")
        print(f"   API: {args.api_url}")
        print(f"   分享: {args.share}")
        print(f"   调试: {args.debug}")
        
        # 创建UI实例
        ui = SimpleCodeDeveloperUI(api_base_url=args.api_url)
        
        # 启动界面
        ui.launch(
            server_name=args.host,
            server_port=args.port,
            share=args.share,
            debug=args.debug,
            show_error=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Simple Code Developer UI 已关闭")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        if args.debug:
            traceback.print_exc()


if __name__ == "__main__":
    main()