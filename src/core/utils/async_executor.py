"""
异步执行器工具
将同步操作包装为异步调用，避免阻塞事件循环
"""

import asyncio
import functools
from concurrent.futures import ThreadPoolExecutor
from typing import Any, Callable, Optional, TypeVar
from agentbay.session import Session
from ...common.config import settings
from ...common.logging import logger

T = TypeVar('T')

class AsyncExecutor:
    """异步执行器，用于将同步调用转换为异步调用"""
    
    _instance: Optional['AsyncExecutor'] = None
    _executor: Optional[ThreadPoolExecutor] = None
    
    def __new__(cls) -> 'AsyncExecutor':
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化线程池"""
        if self._executor is None:
            # 从配置中获取线程池大小，默认为10
            max_workers = settings.executor.max_workers
            self._executor = ThreadPoolExecutor(
                max_workers=max_workers,
                thread_name_prefix="waiy_async_"
            )
            logger.info(f"初始化AsyncExecutor，线程池大小: {max_workers}，超时设置: {settings.executor.timeout}秒")
    
    async def run(self, func: Callable[..., T], *args, timeout: Optional[int] = None, **kwargs) -> T:
        """
        在线程池中异步执行同步函数
        
        Args:
            func: 要执行的同步函数
            *args: 位置参数
            timeout: 超时时间（秒），None表示使用默认配置
            **kwargs: 关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            asyncio.TimeoutError: 当执行超时时
        """
        loop = asyncio.get_event_loop()
        
        # 获取超时设置
        if timeout is None:
            timeout = settings.executor.timeout
        
        # 使用functools.partial预设参数
        if kwargs:
            func_with_kwargs = functools.partial(func, **kwargs)
            future = loop.run_in_executor(self._executor, func_with_kwargs, *args)
        else:
            future = loop.run_in_executor(self._executor, func, *args)
        
        # 添加超时控制
        try:
            return await asyncio.wait_for(future, timeout=timeout)
        except asyncio.TimeoutError:
            logger.error(f"异步执行超时: {func.__name__}，超时时间: {timeout}秒")
            raise
    
    def shutdown(self, wait: bool = True):
        """关闭线程池"""
        if self._executor:
            logger.info("关闭AsyncExecutor线程池")
            self._executor.shutdown(wait=wait)
            self._executor = None

# 全局实例
async_executor = AsyncExecutor()

def async_wrap(func: Callable[..., T]) -> Callable[..., asyncio.Future[T]]:
    """
    装饰器：将同步函数转换为异步函数
    
    Args:
        func: 要包装的同步函数
        
    Returns:
        异步函数
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs) -> T:
        return await async_executor.run(func, *args, **kwargs)
    
    return wrapper

class AsyncAgentBayWrapper:
    """AgentBay SDK的异步包装器"""
    
    def __init__(self, agent_bay):
        """
        初始化包装器
        
        Args:
            agent_bay: AgentBay SDK实例
        """
        self._agent_bay = agent_bay
    
    async def create_session(self, session_params):
        """异步创建会话"""
        return await async_executor.run(self._agent_bay.create, session_params)
    
    async def get_context(self, session_id: str, create_if_not_exists: bool = True):
        """异步获取上下文"""
        return await async_executor.run(
            self._agent_bay.context.get, 
            session_id, 
            create_if_not_exists
        )

class AsyncSessionWrapper:
    """Session的异步包装器"""
    
    def __init__(self, session):
        """
        初始化包装器
        
        Args:
            session: Session实例
        """
        self._session = session
    
    async def get_info(self):
        """异步获取会话信息"""
        return await async_executor.run(self._session.info)
    
    async def execute_task(self, task: str, timeout_s: int):
        """异步执行任务"""
        return await async_executor.run(
            self._session.agent.execute_task,
            task,
            timeout_s
        )
    
    async def sync_context(self):
        """异步同步上下文"""
        return await async_executor.run(self._session.context.sync)
    

def create_async_agentbay_wrapper(agent_bay) -> AsyncAgentBayWrapper:
    """创建AgentBay的异步包装器"""
    return AsyncAgentBayWrapper(agent_bay)

def create_async_session_wrapper( session: Session) -> AsyncSessionWrapper:
    """创建Session的异步包装器"""
    return AsyncSessionWrapper(session)
