from typing import Any, Callable, List, Optional

from agents import (
    Agent,
    ItemHelpers,
    ModelSettings,
    RunConfig,
    Runner,
    RunResult,
    RunResultStreaming,
    TContext,
    custom_span,
)
from agents.tracing.scope import Scope
from memory import Memory, Message, Role
from memory.events import (
    BaseEvent,
    Event,
    EventType,
    TextMessageDeltaContentEvent,
    TextMessageStartEvent,
)
from openai.types.responses import ResponseTextDeltaEvent

from src.common.config.llm_config import model_need_streaming
from src.common.logging import logger

from ..guardrails import waiy_input_guardrail, waiy_output_guardrail
from ..memory.initialization import get_memory


class WaiyBaseAgent(Agent[TContext]):
    """
    This is a custom implementation of the OpenAI Agent class that supports output parsing
    for models that don't support structured output types. The user can specify an output_parser
    function that will be called with the raw output from the agent. This can run custom logic
    such as cleaning up the output and converting it to a structured JSON object.

    Needs to be run with the ResearchRunner to work.
    """

    def __init__(self, *args, output_parser: Optional[Callable[[str], Any]] = None, **kwargs):
        # The output_parser is a function that only takes effect if output_type is not specified
        self.output_parser = output_parser

        # If both are specified, we raise an error - they can't be used together
        if self.output_parser and kwargs.get("output_type"):
            raise ValueError("Cannot specify both output_parser and output_type")

        # 自动设置WAIY护栏（如果用户没有自定义护栏）
        if "input_guardrails" not in kwargs:
            kwargs["input_guardrails"] = [waiy_input_guardrail]
            logger.info("已为WaiyBaseAgent自动添加WAIY输入护栏")

        if "output_guardrails" not in kwargs:
            kwargs["output_guardrails"] = [waiy_output_guardrail]
            logger.info("已为WaiyBaseAgent自动添加WAIY输出护栏")
        super().__init__(*args, **kwargs)

    async def parse_output(self, run_result: RunResult) -> RunResult:
        """
        Process the RunResult by applying the output_parser to its final_output if specified.
        This preserves the RunResult structure while modifying its content.
        """
        if self.output_parser:
            raw_output = run_result.final_output
            parsed_output = self.output_parser(raw_output)
            run_result.final_output = parsed_output
        return run_result


class WaiyRunner(Runner):
    """
    Custom implementation of the OpenAI Runner class that supports output parsing
    for models that don't support structured output types with tools.

    Needs to be run with the WaiyBaseAgent class.
    """

    @classmethod
    async def run(cls, *args, **kwargs) -> None | RunResult | RunResultStreaming:
        """
        Run the agent and process its output with the custom parser if applicable.
        """
        try:
            # Get the starting agent
            starting_agent = kwargs.get("starting_agent") or args[0]
            need_streaming = kwargs.get("streaming_mode") or True
            disable_memory = kwargs.get("disable_memory") or False
            if "streaming_mode" in kwargs:
                del kwargs["streaming_mode"]
            if "disable_memory" in kwargs:
                del kwargs["disable_memory"]
            run_config = kwargs.get("run_config")
            if run_config is None:
                kwargs["run_config"] = RunConfig(
                    trace_include_sensitive_data=True,
                )
            else:
                run_config.trace_include_sensitive_data = True

            if starting_agent is None:
                logger.warning("starting_agent is None, no need to run")
                return None

            result = None

            current_trace = Scope.get_current_trace()
            memory: Memory = get_memory()
            session_id = current_trace.export().get("group_id")
            run_id = current_trace.export().get("id")
            message_id = None
            with custom_span(starting_agent.name, data={"disable_memory": disable_memory}):
                try:
                    if need_streaming and model_need_streaming(starting_agent.model):
                        logger.info("使用流式模式")
                        result = Runner.run_streamed(*args, **kwargs)
                        try:
                            async for event in result.stream_events():
                                """处理流式事件"""
                                try:
                                    if event.type == "raw_response_event" and not disable_memory:
                                        message_id = cls._handle_raw_response_event(
                                            event, memory, session_id, run_id, message_id
                                        )

                                    elif event.type == "agent_updated_stream_event":
                                        logger.info(
                                            f"stream_event -- Agent 更新: {event.new_agent.name}"
                                        )
                                    elif event.type == "run_item_stream_event":
                                        if event.item.type == "tool_call_item":
                                            logger.info("stream_event -- 工具被调用")
                                        elif event.item.type == "tool_call_output_item":
                                            logger.info(
                                                f"stream_event -- 工具输出: {event.item.output}"
                                            )
                                        elif event.item.type == "message_output_item":
                                            logger.info(
                                                f"stream_event -- 消息输出:\n {ItemHelpers.text_message_output(event.item)}"
                                            )
                                        else:
                                            pass  # 忽略其他事件类型
                                except Exception as event_error:
                                    logger.error(f"处理流式事件时发生异常: {event_error}")
                                    # 继续处理下一个事件，不中断整个流
                                    continue
                        except Exception as stream_error:
                            logger.error(f"处理流式响应时发生异常: {stream_error}")
                            raise stream_error
                    else:
                        logger.info("使用非流式模式")
                        # Call the original run method
                        result = await Runner.run(*args, **kwargs)
                except Exception as run_error:
                    logger.error(f"运行智能体时发生异常: {run_error}")
                    raise run_error

                # If the starting agent is of type WaiyBaseAgent, parse the output
                if isinstance(starting_agent, WaiyBaseAgent) and result is not None:
                    try:
                        return await starting_agent.parse_output(result)
                    except Exception as parse_error:
                        logger.error(f"解析输出时发生异常: {parse_error}")
                        # 如果解析失败，返回原始结果而不是抛出异常
                        logger.warning("输出解析失败，返回原始结果")
                        return result

                return result

        except Exception as general_error:
            logger.error(f"WaiyRunner.run 执行过程中发生未知异常: {general_error}")
            # 可以选择重新抛出异常或返回None，这里选择抛出以便上层处理
            raise general_error

    @classmethod
    def _handle_raw_response_event(
        cls, event, memory: Memory, session_id: str, run_id: str, message_id: Optional[str]
    ) -> Optional[str]:
        """
        处理raw_response_event事件

        Args:
            event: 原始响应事件
            memory: Memory实例
            session_id: 会话ID
            run_id: 运行ID
            message_id: 消息ID

        Returns:
            更新后的message_id
        """
        try:
            if event.data.sequence_number == 0:
                message_id = None
                events = memory.list_events(
                    session_id=session_id,
                    include_event_types=["TEXT_MESSAGE_START"],
                    order_by="desc",
                    page_size=1,
                )
                events_list = events.get("events", [])
                if events_list and len(events_list) > 0 and isinstance(
                    events_list[0], TextMessageStartEvent
                ):
                    message_id = events_list[0].message_id
                else:
                    logger.error(f"流失消息未找到TextMessageStartEvent")

            if isinstance(event.data, ResponseTextDeltaEvent):
                memory.add_event(
                    TextMessageDeltaContentEvent(
                        role="assistant",
                        session_id=session_id,
                        run_id=run_id,
                        message_id=message_id,
                        content=event.data.delta,
                    )
                )

            return message_id
        except Exception as e:
            logger.error(f"处理raw_response_event时发生异常: {e}")
            return message_id
