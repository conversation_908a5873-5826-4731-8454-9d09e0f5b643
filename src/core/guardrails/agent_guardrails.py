"""
WAIY智能体护栏

基于OpenAI Agents SDK的护栏机制实现输入检测
"""

from typing import List, Union, Optional
from agents import Agent, input_guardrail, output_guardrail, GuardrailFunctionOutput, RunContextWrapper, TResponseInputItem

from src.common.config.settings import settings
from src.common.logging import logger
from .input_guardrail import WaiyGuardrail
from .output_guardrail import WaiyOutputGuardrail
from .exceptions import GuardrailViolationException
from pydantic import BaseModel

# 全局WaiyGuardrail实例
_waiy_guardrail_instance: Optional[WaiyGuardrail] = None
_waiy_output_guardrail_instance: Optional[WaiyOutputGuardrail] = None


def get_waiy_guardrail() -> Optional[WaiyGuardrail]:
    """获取全局WaiyGuardrail实例"""
    global _waiy_guardrail_instance
    
    if _waiy_guardrail_instance is None:
        try:
            guardrail_config = settings.guardrail
            if guardrail_config.enabled:
                _waiy_guardrail_instance = WaiyGuardrail(
                    app_key=guardrail_config.content_check_app_key,
                    app_secret=guardrail_config.content_check_app_secret,
                    enabled=guardrail_config.enabled,
                    strict_mode=guardrail_config.strict_mode
                )
                logger.info("全局WaiyGuardrail实例初始化成功")
            else:
                logger.info("WaiyGuardrail已禁用")
        except Exception as e:
            logger.error(f"初始化WaiyGuardrail失败: {e}", exc_info=True)
    
    return _waiy_guardrail_instance


def get_waiy_output_guardrail() -> Optional[WaiyOutputGuardrail]:
    """获取全局WaiyOutputGuardrail实例"""
    global _waiy_output_guardrail_instance
    
    if _waiy_output_guardrail_instance is None:
        try:
            guardrail_config = settings.guardrail
            if guardrail_config.enabled:
                _waiy_output_guardrail_instance = WaiyOutputGuardrail(
                    app_key=guardrail_config.content_check_app_key,
                    app_secret=guardrail_config.content_check_app_secret,
                    enabled=guardrail_config.enabled,
                    strict_mode=guardrail_config.strict_mode
                )
                logger.info("全局WaiyOutputGuardrail实例初始化成功")
            else:
                logger.info("WaiyOutputGuardrail已禁用")
        except Exception as e:
            logger.error(f"初始化WaiyOutputGuardrail失败: {e}", exc_info=True)
    
    return _waiy_output_guardrail_instance


@input_guardrail
async def waiy_input_guardrail(
    ctx: RunContextWrapper[None],
    agent: Agent,
    input_data: Union[str, List[TResponseInputItem]]
) -> GuardrailFunctionOutput:
    """
    WAIY输入护栏
    
    基于OpenAI Agents SDK的护栏机制实现输入检测
    
    Args:
        ctx: 运行上下文
        agent: 智能体实例
        input_data: 输入数据（字符串或消息列表）
    
    Returns:
        GuardrailFunctionOutput: 护栏检测结果
    """
    try:
        # 获取WaiyGuardrail实例
        guardrail = get_waiy_guardrail()
        
        if not guardrail:
            # 护栏未启用，直接通过
            return GuardrailFunctionOutput(
                output_info="WaiyGuardrail已禁用，跳过检测",
                tripwire_triggered=False
            )
        
        # 提取文本内容进行检测
        text_content = _extract_text_from_input(input_data)
        
        if not text_content:
            # 没有文本内容，直接通过
            return GuardrailFunctionOutput(
                output_info="无文本内容，跳过检测",
                tripwire_triggered=False
            )
        
        # 执行护栏检测
        try:
            user_id =getattr(ctx.context, 'session_context', {}).get("user_id") or "unknown"
            result = guardrail.check_input(text_content, user_id)
            
            if result.is_safe:
                logger.info(f"WaiyGuardrail检测通过: {result.message}")
                return GuardrailFunctionOutput(
                    output_info=f"输入检测通过: {result.message}",
                    tripwire_triggered=False
                )
            else:
                logger.warning(f"WaiyGuardrail检测不通过: {result.message}")
                return GuardrailFunctionOutput(
                    output_info=f"输入被拦截: {result.message}",
                    tripwire_triggered=True
                )
                
        except GuardrailViolationException as e:
            logger.error(f"WaiyGuardrail检测到违规: {e}  内容：{text_content}")
            return GuardrailFunctionOutput(
                output_info=f"输入违规: {e.violation_type} - {str(e)}",
                tripwire_triggered=True
            )
            
    except Exception as e:
        logger.exception(f"WaiyGuardrail护栏执行异常: {e}", exc_info=True)
        # 异常时不触发tripwire，允许继续执行避免误拦截
        return GuardrailFunctionOutput(
            output_info=f"护栏检测异常: {str(e)}",
            tripwire_triggered=False
        )


@output_guardrail
async def waiy_output_guardrail(
    ctx: RunContextWrapper[None],
    agent: Agent,
    output: str
) -> GuardrailFunctionOutput:
    """
    WAIY输出护栏
    
    基于OpenAI Agents SDK的护栏机制实现输出检测
    
    Args:
        ctx: 运行上下文
        agent: 智能体实例
        output: 智能体输出内容
    
    Returns:
        GuardrailFunctionOutput: 护栏检测结果
    """
    try:
        # 获取WaiyOutputGuardrail实例
        guardrail = get_waiy_output_guardrail()
        
        if not guardrail:
            # 护栏未启用，直接通过
            return GuardrailFunctionOutput(
                output_info="WaiyOutputGuardrail已禁用，跳过检测",
                tripwire_triggered=False
            )
        
        text_content = ""
        if isinstance(output, BaseModel):
            text_content = str(output)
        elif isinstance(output, str):
            text_content = output
        else:
            text_content = str(output) if output else ""
        
        if not text_content or not text_content.strip():
            # 没有输出内容，直接通过
            return GuardrailFunctionOutput(
                output_info="无输出内容，跳过检测",
                tripwire_triggered=False
            )
        
        # 执行护栏检测
        try:
            user_id = ctx.context.session_context.get("user_id") or "unknown"
            result = guardrail.check_output(text_content, user_id)
            
            if result.is_safe:
                logger.info(f"WaiyOutputGuardrail检测通过: {result.message}")
                return GuardrailFunctionOutput(
                    output_info=f"输出检测通过: {result.message}",
                    tripwire_triggered=False
                )
            else:
                logger.warning(f"WaiyOutputGuardrail检测不通过: {result.message}")
                return GuardrailFunctionOutput(
                    output_info=f"输出被拦截: {result.message}",
                    tripwire_triggered=True
                )
                
        except GuardrailViolationException as e:
            logger.error(f"WaiyOutputGuardrail检测到违规: {e} 内容：{text_content}")
            return GuardrailFunctionOutput(
                output_info=f"输出违规: {e.violation_type} - {str(e)}",
                tripwire_triggered=True
            )
            
    except Exception as e:
        logger.exception(f"WaiyOutputGuardrail护栏执行异常: {e}", exc_info=True)
        # 异常时不触发tripwire，允许继续执行避免误拦截
        return GuardrailFunctionOutput(
            output_info=f"护栏检测异常: {str(e)}",
            tripwire_triggered=False
        )

def _extract_text_from_input(input_data: Union[str, List[TResponseInputItem]]) -> str:
    """
    从输入数据中提取最新用户消息的文本内容
    
    Args:
        input_data: 输入数据
        
    Returns:
        str: 提取的最新用户消息文本内容
    """
    if isinstance(input_data, str):
        return input_data
    
    if isinstance(input_data, list):
        # 从后往前遍历，找到最新的用户消息
        for item in reversed(input_data):
            # 检查是否是用户消息
            is_user_message = False
            text_content = ""
            
            if hasattr(item, 'role') and item.role == 'user':
                is_user_message = True
                if hasattr(item, 'content') and isinstance(item.content, str):
                    text_content = item.content
                elif hasattr(item, 'text') and isinstance(item.text, str):
                    text_content = item.text
            elif isinstance(item, dict):
                # 处理字典格式的消息
                if item.get('role') == 'user':
                    is_user_message = True
                    content = item.get('content', '')
                    if isinstance(content, str):
                        text_content = content
                    elif isinstance(content, list):
                        # 处理多模态消息中的文本部分
                        text_parts = []
                        for part in content:
                            if isinstance(part, dict) and part.get('type') == 'text':
                                text_parts.append(part.get('text', ''))
                        text_content = ' '.join(text_parts)
            
            # 如果找到用户消息且有文本内容，返回该内容
            if is_user_message and text_content.strip():
                return text_content
        
        # 如果没有找到用户消息，返回空字符串
        return ""
    
    return str(input_data) if input_data else ""


