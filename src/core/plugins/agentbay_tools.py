"""
AgentBay SDK工具封装
提供Agent可调用的AgentBay功能函数
"""
import os
from typing import Dict, Any
from functools import wraps
from agents import function_tool

from src.core.utils.async_executor import create_async_agentbay_wrapper, create_async_session_wrapper
from agentbay import AgentBay, Config, CreateSessionParams, SyncPolicy, ContextSync



# 全局会话管理
_global_session = None
_async_session = None


def agentbay_session_required(func):
    """装饰器：确保AgentBay会话可用"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        if AgentBay is None:
            return {
                "success": False,
                "error": "AgentBay SDK not available",
                "message": "AgentBay functionality requires proper SDK installation and configuration"
            }
        
        global _global_session, _async_session
        if _global_session is None or _async_session is None:
            return {
                "success": False,
                "error": "Session not initialized",
                "message": "AgentBay session not initialized. Call create_agentbay_session first."
            }
        return await func(*args, **kwargs)
    return wrapper


@function_tool
async def create_agentbay_session(
    context_name: str = "code_workspace",
    resource_type: str = "agentbay",
    image: str = "code_latest",
    token: str = "ako-a18b5d79-dcb5-4771-8ed4-60916cb7c58c"
) -> Dict[str, Any]:
    """
    创建AgentBay会话
    
    Args:
        context_name: 上下文名称，用于数据持久化
        resource_type: 资源类型，agentbay或desktop
        image: 镜像类型
        
    Returns:
        会话信息字典
    """
    if AgentBay is None:
        return {
            "success": False,
            "error": "AgentBay SDK not available",
            "message": "AgentBay functionality requires proper SDK installation and configuration"
        }
    
    global _global_session, _async_session
    
    try:
        # 获取AgentBay配置
        config = Config(
            region_id="ap-southeast-1",
		    endpoint="wuyingai.ap-southeast-1.aliyuncs.com",
    		timeout_ms=60000,
        )
        agent_bay = AgentBay(
            api_key=token,
            cfg=config
        )
        
        # 创建或获取持久化上下文
        async_agent_bay = create_async_agentbay_wrapper(agent_bay)

        
        # 创建会话参数
        params = CreateSessionParams(
            image_id=image,
            labels={"purpose": "code_development", "app": "code_developer"}
        )
        
        # 创建会话
        session_result = await async_agent_bay.create_session(params)
        _global_session = session_result.session
        _async_session = create_async_session_wrapper(_global_session)
        
        return {
            "success": True,
            "session_id": _global_session.get_session_id(),
            "message": f"Successfully created AgentBay session with context '{context_name}'",
            "workspace_path": "/workspace"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to create AgentBay session"
        }


@function_tool
@agentbay_session_required
async def write_code_file(file_path: str, content: str, mode: str = "overwrite") -> Dict[str, Any]:
    """
    写入代码文件到AgentBay会话
    
    Args:
        file_path: 文件路径（相对于/workspace）
        content: 文件内容
        mode: 写入模式，overwrite或append
        
    Returns:
        操作结果字典
    """
    try:
        # 确保路径以/workspace开头
        if not file_path.startswith("/workspace"):
            file_path = f"/workspace/{file_path.lstrip('/')}"
            
        # 确保目录存在
        dir_path = os.path.dirname(file_path)
        if dir_path != "/workspace":
            _global_session.file_system.create_directory(dir_path)
        
        # 写入文件
        result = _global_session.file_system.write_file(file_path, content, mode)
        
        return {
            "success": result.success,
            "file_path": file_path,
            "message": f"Successfully wrote file: {file_path}" if result.success else f"Failed to write file: {result.error}"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Error writing file {file_path}"
        }


@function_tool
@agentbay_session_required
async def read_code_file(file_path: str) -> Dict[str, Any]:
    """
    读取代码文件内容
    
    Args:
        file_path: 文件路径（相对于/workspace）
        
    Returns:
        文件内容和操作结果
    """
    try:
        # 确保路径以/workspace开头
        if not file_path.startswith("/workspace"):
            file_path = f"/workspace/{file_path.lstrip('/')}"
        
        # 读取文件
        result = _global_session.file_system.read_file(file_path)
        
        if result.success:
            return {
                "success": True,
                "file_path": file_path,
                "content": result.content,
                "message": f"Successfully read file: {file_path}"
            }
        else:
            return {
                "success": False,
                "file_path": file_path,
                "error": result.error,
                "message": f"Failed to read file: {file_path}"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Error reading file {file_path}"
        }


@function_tool
@agentbay_session_required
async def execute_code(code: str, language: str = "python", timeout: int = 300) -> Dict[str, Any]:
    """
    在AgentBay会话中执行代码
    
    Args:
        code: 要执行的代码
        language: 编程语言，python或javascript
        timeout: 超时时间（秒）
        
    Returns:
        执行结果字典
    """
    try:
        result = _global_session.code.run_code(code, language, timeout)
        
        return {
            "success": result.success,
            "output": result.result if result.success else None,
            "error": result.error_message if not result.success else None,
            "message": "Code executed successfully" if result.success else f"Code execution failed: {result.error_message}"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Error executing code"
        }


@function_tool
@agentbay_session_required
async def execute_shell_command(command: str, timeout: int = 30000) -> Dict[str, Any]:
    """
    执行Shell命令
    
    Args:
        command: Shell命令
        timeout: 超时时间（毫秒）
        
    Returns:
        命令执行结果
    """
    try:
        result = _global_session.command.execute_command(command, timeout)
        
        return {
            "success": result.success,
            "stdout": result.stdout if result.success else None,
            "stderr": result.stderr if result.success else None,
            "exit_code": result.exit_code if hasattr(result, 'exit_code') else None,
            "message": "Command executed successfully" if result.success else f"Command failed: {result.error}"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Error executing command: {command}"
        }


@function_tool
@agentbay_session_required
async def list_workspace_files(directory: str = "/workspace") -> Dict[str, Any]:
    """
    列出工作空间文件
    
    Args:
        directory: 目录路径
        
    Returns:
        文件列表
    """
    try:
        result = _global_session.file_system.list_directory(directory)
        
        if result.success:
            files = []
            for item in result.items:
                files.append({
                    "name": item.name,
                    "path": item.path,
                    "type": item.type,
                    "size": getattr(item, 'size', None)
                })
            
            return {
                "success": True,
                "directory": directory,
                "files": files,
                "message": f"Successfully listed {len(files)} items in {directory}"
            }
        else:
            return {
                "success": False,
                "error": result.error_message,
                "message": f"Failed to list directory: {directory}"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Error listing directory {directory}"
        }


@function_tool
@agentbay_session_required
async def install_python_package(package_name: str) -> Dict[str, Any]:
    """
    安装Python包
    
    Args:
        package_name: 包名称
        
    Returns:
        安装结果
    """
    command = f"pip install {package_name}"
    result = await execute_shell_command(command, timeout=120000)  # 2分钟超时
    
    if result["success"]:
        return {
            "success": True,
            "package": package_name,
            "message": f"Successfully installed {package_name}",
            "output": result.get("stdout", "")
        }
    else:
        return {
            "success": False,
            "package": package_name,
            "error": result.get("stderr", result.get("error", "")),
            "message": f"Failed to install {package_name}"
        }


@function_tool
@agentbay_session_required
async def start_python_server(file_path: str, port: int = 8000, background: bool = True) -> Dict[str, Any]:
    """
    启动Python服务器
    
    Args:
        file_path: Python文件路径
        port: 端口号
        background: 是否后台运行
        
    Returns:
        启动结果
    """
    try:
        # 确保路径以/workspace开头
        if not file_path.startswith("/workspace"):
            file_path = f"/workspace/{file_path.lstrip('/')}"
        
        # 构建启动命令
        if background:
            command = f"cd /workspace && python {file_path.replace('/workspace/', '')} --port {port} &"
        else:
            command = f"cd /workspace && python {file_path.replace('/workspace/', '')} --port {port}"
        
        result = await execute_shell_command(command, timeout=10000)
        
        return {
            "success": True,
            "file_path": file_path,
            "port": port,
            "background": background,
            "message": f"Started Python server on port {port}",
            "command_result": result
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Error starting server: {file_path}"
        }


@function_tool
@agentbay_session_required
async def check_process_status(process_name: str = "python") -> Dict[str, Any]:
    """
    检查进程状态
    
    Args:
        process_name: 进程名称
        
    Returns:
        进程状态信息
    """
    command = f"ps aux | grep {process_name} | grep -v grep"
    result = await execute_shell_command(command)
    
    if result["success"]:
        processes = []
        if result["stdout"]:
            for line in result["stdout"].strip().split('\n'):
                if line.strip():
                    processes.append(line.strip())
        
        return {
            "success": True,
            "process_name": process_name,
            "processes": processes,
            "count": len(processes),
            "message": f"Found {len(processes)} {process_name} processes"
        }
    else:
        return {
            "success": False,
            "error": result.get("error", ""),
            "message": f"Failed to check {process_name} processes"
        }


@function_tool
@agentbay_session_required
async def get_session_link(
    protocol_type: str = None,
    port: int = None
) -> Dict[str, Any]:
    """
    获取AgentBay会话访问链接
    
    Args:
        protocol_type: 协议类型 (可选), 填写 "https"
        port: 端口号 (可选)
        
    Returns:
        包含链接URL的结果字典
    """
    try:
        result = _global_session.get_link(protocol_type=protocol_type, port=port)
        
        if result.success:
            return {
                "success": True,
                "url": result.data,
                "request_id": result.request_id,
                "message": f"Successfully retrieved session link"
            }
        else:
            return {
                "success": False,
                "error": result.error_message,
                "message": "Failed to get session link"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Error getting session link"
        }


@function_tool
async def close_agentbay_session() -> Dict[str, Any]:
    """
    关闭AgentBay会话
    
    Returns:
        关闭结果
    """
    if AgentBay is None:
        return {
            "success": False,
            "error": "AgentBay SDK not available",
            "message": "AgentBay functionality requires proper SDK installation and configuration"
        }
    
    global _global_session, _async_session
    
    try:
        if _global_session:
            _global_session.delete(sync_context=True)
            _global_session = None
            _async_session = None
            
            return {
                "success": True,
                "message": "Successfully closed AgentBay session and synced context"
            }
        else:
            return {
                "success": True,
                "message": "No active AgentBay session to close"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Error closing AgentBay session"
        }


# 工具函数注册列表
AGENTBAY_TOOLS = [
    create_agentbay_session,
    write_code_file,
    read_code_file,
    execute_code,
    execute_shell_command,
    list_workspace_files,
    install_python_package,
    start_python_server,
    check_process_status,
    get_session_link,
    close_agentbay_session
]