"""
Code Developer应用
专业的Python代码开发和部署助手
"""

from src.applications.base import BaseApplication
from src.applications.code_developer.prompts import CODE_DEVELOPER_INSTRUCTIONS
from src.common.models import AppMetadata, Visibility
from src.common.config.llm_config import llm_config_global
from src.core.plugins.agentbay_tools import AGENTBAY_TOOLS


class CodeDeveloperApp(BaseApplication):
    """Code Developer应用类"""  

    @property
    def metadata(self) -> AppMetadata:
        """应用元数据"""
        return AppMetadata(
            id="code_developer",
            name="Code Developer",
            version="1.0.0",
            description="Professional Python code development and deployment assistant using AgentBay cloud environments",
            tags=["编程", "开发", "Python", "部署", "云端", "服务器"],
            visibility=Visibility.PRIVATE,
            mcp_servers=[]  # 不需要额外的MCP服务器，使用内置工具
        )

    async def setup_agents(self) -> None:
        """设置智能体"""
        # 获取LLM配置
        llm_model = llm_config_global.get_model(model_name="claude_sonnet4", model_provider="others")
        
        # 创建主要的代码开发智能体
        self.primary_agent = await self.create_agent(
            name="code_developer",
            instructions=CODE_DEVELOPER_INSTRUCTIONS,
            is_primary=True,
            tools=AGENTBAY_TOOLS,  # 使用封装的AgentBay工具
            model=llm_model
        )
