# Code Developer Application

专业的Python代码开发和部署助手，基于AgentBay云环境实现代码开发、部署和运行。

## 功能特性

### 核心能力
- **代码编写**: 创建Python应用程序、Web服务器、API和脚本
- **云端部署**: 将代码部署到AgentBay云环境
- **包管理**: 安装和管理Python包
- **服务器管理**: 启动、监控和管理Python服务器
- **文件操作**: 管理项目文件和目录结构
- **代码执行**: 在云环境中运行和测试Python代码

### 支持的框架
- Flask Web应用
- FastAPI服务
- Django项目
- 通用Python脚本

## 工具功能

### 会话管理
- `create_agentbay_session`: 创建AgentBay云环境会话
- `close_agentbay_session`: 关闭会话并同步上下文

### 文件操作
- `write_code_file`: 写入代码文件到云环境
- `read_code_file`: 读取代码文件内容
- `list_workspace_files`: 列出工作空间文件

### 代码执行
- `execute_code`: 在云环境中执行Python代码
- `execute_shell_command`: 执行Shell命令

### 包和服务器管理
- `install_python_package`: 安装Python包
- `start_python_server`: 启动Python服务器
- `check_process_status`: 检查进程状态

## 使用示例

### 1. 创建简单Flask应用

```
用户: 帮我创建一个简单的Flask Web服务器，包含健康检查接口

助手会：
1. 创建AgentBay会话
2. 编写Flask应用代码
3. 安装Flask依赖
4. 部署代码到云环境
5. 启动服务器
6. 验证服务状态
```

### 2. 部署FastAPI应用

```
用户: 开发一个FastAPI应用，提供用户管理API

助手会：
1. 设置云环境
2. 创建FastAPI应用结构
3. 实现用户管理接口
4. 安装必要依赖
5. 启动和测试服务
```

### 3. 数据处理脚本

```
用户: 写一个数据分析脚本处理CSV文件

助手会：
1. 创建工作环境
2. 编写数据处理脚本
3. 安装pandas等依赖
4. 部署和运行脚本
5. 展示处理结果
```

## 工作流程

1. **需求分析**: 理解用户的编程需求和项目目标
2. **环境设置**: 创建和配置AgentBay云环境
3. **代码开发**: 编写清洁、高效的Python代码
4. **依赖管理**: 安装所需包和库
5. **部署**: 将代码文件部署到云工作空间
6. **执行测试**: 运行和测试应用程序
7. **服务管理**: 启动服务并监控状态

## 最佳实践

### 代码质量
- 编写清洁、有文档的Python代码
- 包含错误处理和日志记录
- 使服务器可配置（端口、主机等）
- 使用适当的项目结构

### 安全性
- 不在代码中暴露敏感信息
- 使用环境变量进行配置
- 实现适当的错误处理
- 遵循Python编码标准(PEP 8)

## 技术架构

- **基础框架**: waiy_infra多智能体框架
- **云环境**: AgentBay云执行环境
- **支持语言**: Python 3.x
- **部署方式**: 云端容器化部署
- **持久化**: 上下文同步确保数据持久化

## 配置要求

确保AgentBay配置正确：
- API密钥设置
- 端点地址配置
- 网络访问权限

## 故障排除

### 常见问题
1. **会话创建失败**: 检查AgentBay API密钥和网络连接
2. **包安装失败**: 确认包名正确，检查网络连接
3. **服务器启动失败**: 检查端口占用，验证代码语法
4. **文件操作失败**: 确认路径正确，检查权限设置

### 调试方法
- 使用`list_workspace_files`查看文件结构
- 使用`check_process_status`监控进程状态
- 查看命令执行结果的错误信息
- 检查代码执行的输出和错误