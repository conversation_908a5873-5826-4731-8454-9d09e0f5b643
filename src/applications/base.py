"""
基础应用类
提供所有应用的基础功能和接口
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from typing import Any, Callable, Dict, List, Optional, Tuple

from agents import gen_trace_id, trace
from memory import Memory, Message, Role
from memory.events import (
    EventType,
    RunErrorEvent,
    RunFinishedEvent,
    RunStartedEvent,
    RunStoppedEvent,
)
from pydantic import BaseModel

from ..common.config import settings
from ..common.logging import logger
from ..common.models import AppMetadata
from ..core.agent.baseclass import WaiyBaseAgent, WaiyRunner
from ..core.mcp import MCPServerManager
from ..core.memory.initialization import get_memory
from ..core.memory.session import WaiySession


class DefaultAgentContext(BaseModel):
    session_context: Dict[str, Any]


class BaseApplication[TContext](ABC):
    """应用基类 - 支持单智能体和多智能体场景"""

    def __init__(
        self,
        max_turns: Optional[int] = 25,
        context: Optional[Dict[str, Any]] = None,
    ):
        # 处理context参数
        self.context = context or {}
        self.session_context = self.context
        self.agent_context: Optional[TContext] = DefaultAgentContext(
            session_context=self.session_context
        )

        self.max_turns = max_turns

        # 提取resources参数
        self.chat_resources = self.context.get("resources", [])

        self.session_id = self.context.get("session_id") or f"session-{uuid.uuid4()}"
        self.trace_id = self.context.get("trace_id") or gen_trace_id()

        self.runtime_resource = self.context.get("runtime_resource") or {}

        # 从context中解析model_setting，用pydantic转换成ModelSetting对象
        from ..api.models.request_models import ModelSetting

        # 使用 model_validate 而不是 model_construct，确保验证器被调用
        self.model_setting = ModelSetting.model_validate(self.context.get("model_setting", {}))
        if self.model_setting:
            logger.info(f"从context初始化模型设置: {self.model_setting}")

        logger.info(f"模型等级: {self.model_setting.model_level}")
        logger.info(f"初始化应用实例: session_id={self.session_id}, trace_id={self.trace_id}")

        self.server_manager = MCPServerManager(agentbay_session_id=self.session_id)
        self.servers = []

        self.primary_agent: WaiyBaseAgent[TContext] | None = None
        self.all_agents = {}  # 所有智能体字典
        self.memory: Memory = get_memory()

        # 资源处理器
        from ..core.resources import ResourceProcessor

        self.resource_processor = ResourceProcessor()

        self._initialized = False

        self._cancelled = False

    @property
    @abstractmethod
    def metadata(self) -> AppMetadata:
        """获取应用元数据"""
        pass

    async def initialize(self) -> bool:
        """初始化应用"""
        if self._initialized:
            return True

        try:
            # 设置所有MCP服务器
            self.servers = await self.server_manager.setup_servers()
            if not self.servers:
                logger.error(f"无法设置MCP服务器")
                # return False

            # 创建所有智能体
            await self.setup_agents()

            if not self.primary_agent and self.all_agents:
                # 如果没有显式设置主智能体，使用第一个创建的智能体
                self.primary_agent = next(iter(self.all_agents.values()))

            self._initialized = True
            return True
        except asyncio.CancelledError:
            logger.info("任务已被取消")
            raise
        except Exception as e:
            logger.error(f"初始化应用失败: {e}", exc_info=True)
            return False

    @abstractmethod
    async def setup_agents(self) -> None:
        """设置智能体"""
        pass

    async def create_agent(
        self,
        name: str,
        instructions: str,
        is_primary: bool = False,
        mcp_servers: Optional[List[str]] = None,
        **kwargs,
    ) -> WaiyBaseAgent[TContext]:
        """
        创建智能体

        Args:
            name: 智能体名称
            instructions: 指令内容
            is_primary: 是否为主智能体
            mcp_servers: 智能体需要的MCP服务器类型列表
            **kwargs: 传递给WaiyBaseAgent的其他参数

        Returns:
            创建的智能体
        """
        self._check_cancellation()
        # 准备MCP服务器
        decorated_servers = []
        if mcp_servers:
            for server in self.servers:
                if any(cap in server.name.lower() for cap in mcp_servers):
                    decorated_servers.append(server)
                    # 这里暂时不使用NameOverridingMCPServer，目前工具较少，工具名称冲突问题不明显
                    # decorated_servers.append(NameOverridingMCPServer(server))

        # 构造参数字典，只处理四个特定参数
        agent_kwargs = {
            "name": name,
            "instructions": instructions,
            "mcp_servers": decorated_servers,
            **kwargs,  # 其他参数直接透传
        }

        # 创建智能体
        agent = WaiyBaseAgent(**agent_kwargs)

        # 存储智能体
        self.all_agents[name] = agent

        # 如果标记为主智能体，则设置
        if is_primary:
            self.primary_agent = agent

        return agent

    def _check_cancellation(self):
        """检查是否被取消"""
        if self._cancelled:
            raise asyncio.CancelledError("任务已被取消")
            return
        # 检查memory中的run状态
        if hasattr(self, "memory") and self.memory:
            # 从context中获取run_id，如果没有则跳过检查
            run_id = getattr(self, "run_id", None)
            if run_id:
                try:
                    run = self.memory.get_run(run_id)
                    if run and hasattr(run, "run_status") and run.run_status == "stopped":
                        logger.info(f"检测到run状态为stopped，触发取消，run_id={run_id}")
                        self._cancelled = True
                        raise asyncio.CancelledError("任务已被停止")
                except asyncio.CancelledError:
                    # 如果检查过程中抛出CancelledError，直接重新抛出
                    raise
                except Exception as e:
                    # 如果检查过程中出现其他异常，记录日志但不影响正常流程
                    logger.info(f"检查run状态时出现异常: {e}")
                    pass

    async def process_message(self, message: str) -> Dict[str, Any]:
        """处理用户消息（支持资源增强）"""
        self._check_cancellation()

        # 处理runtime_resource参数
        if self.runtime_resource:
            await self._process_runtime_resource(self.runtime_resource)
        else:
            # 没有传递runtime_resource，等同于type="none"的处理方式
            logger.info("未传递runtime_resource参数，跳过运行时资源处理")
            self.context["runtime_resource_processed"] = True
            self.context["runtime_resource_type"] = "none"

        result = None
        try:
            logger.info(f"[process_message] 输入 message={message}, context={self.context}")
            self.memory.update_session(
                session_id=self.session_id,
                ali_uid=self.context.get("ali_uid"),
                wy_id=self.context.get("wy_id"),
                user_id=self.context.get("user_id"),
                app_id=self.metadata.id,
            )
            self.memory.add_event(
                RunStartedEvent(
                    type=EventType.RUN_STARTED,
                    session_id=self.session_id,
                    run_id=self.trace_id,
                    app_id=self.metadata.id,
                ),
                self.session_id,
            )
            self.memory.add_message(
                Message(
                    role=Role.USER,
                    content=message,
                    app_id=self.metadata.id,
                    run_id=self.trace_id,
                    session_id=self.session_id,
                )
            )
            with trace(
                workflow_name=self.metadata.id, trace_id=self.trace_id, group_id=self.session_id
            ):
                # 处理资源增强
                enhanced_message, resource_context = await self._enhance_message_with_resources(
                    message, self.chat_resources, self.context
                )
                logger.info(
                    f"[process_message] enhanced_message={enhanced_message}, resource_context={resource_context}"
                )

                # 更新context
                self.context.update(resource_context)

                # 记录资源处理信息到记忆中
                if resource_context and resource_context.get("resource_info"):
                    resource_summary = self._build_resource_summary(
                        resource_context["resource_info"]
                    )
                    self.memory.add_message(
                        Message(
                            role=Role.TOOL,
                            tool_name="resource_processor",
                            app_id=self.metadata.id,
                            session_id=self.session_id,
                            trace_id=self.trace_id,
                            content=resource_summary,
                        )
                    )

                # 调用具体应用的处理逻辑
                result = await self._do_process_message(enhanced_message)

                self.memory.add_event(
                    RunFinishedEvent(
                        type=EventType.RUN_FINISHED,
                        session_id=self.session_id,
                        run_id=self.trace_id,
                    ),
                    self.session_id,
                )

                # 添加 trace_id 和 session_id 到结果中
                result["trace_id"] = self.trace_id
                result["session_id"] = self.session_id
                return result
        except asyncio.CancelledError:
            # 在收到terminate请求时，会记录RunStoppedEvent，这里不需要再记录RunFinishedEvent或者RunErrorEvent
            logger.info("任务已被取消")
            raise
        except Exception as e:
            logger.error(f"process_message处理异常, error={e}")
            self.memory.add_event(
                RunErrorEvent(
                    type=EventType.RUN_ERROR,
                    session_id=self.session_id,
                    run_id=self.trace_id,
                    code=e.__class__.__name__,
                    message=f"{e}",
                ),
                self.session_id,
            )
            raise

    async def _do_process_message(self, message: str) -> Dict[str, Any]:
        """处理用户消息"""
        if not self.primary_agent:
            raise RuntimeError("主智能体未初始化")

        try:
            logger.info(f"开始处理消息，session_id={self.session_id}")

            result = await WaiyRunner.run(
                starting_agent=self.primary_agent,
                input=message,
                max_turns=self.max_turns,
                context=self.agent_context,
                session=WaiySession(self.session_id, self.context.get("user_id"), message)
            )
            return {
                "response": result.final_output,
                "turns": len(result.turns) if hasattr(result, "turns") else 1,
            }

        except Exception as e:
            logger.error(
                f"处理消息时发生错误，session_id={self.session_id}, 错误信息={e}",
                exc_info=True,
            )
            raise

    async def _enhance_message_with_resources(
        self, message: str, resources: List[Dict[str, Any]], context: Dict[str, Any]
    ) -> tuple[str, Dict[str, Any]]:
        """
        使用资源增强消息

        子类可以重写这个方法来自定义资源处理逻辑

        Args:
            message: 用户原始消息
            resources: 资源列表
            context: 上下文信息

        Returns:
            tuple: (增强后的消息, 资源上下文)
        """
        if not resources:
            return message, {}

        # 将Dict转换为ResourceRequest对象
        from ..api.models.resource_models import FileResource, KnowledgeBaseResource

        resource_objects = []
        for resource in resources:
            try:
                if resource.get("type") == "knowledge_base":
                    resource_objects.append(KnowledgeBaseResource(**resource))
                elif resource.get("type") == "file":
                    resource_objects.append(FileResource(**resource))
                else:
                    logger.warning(f"未知的资源类型: {resource.get('type')}")
            except Exception as e:
                logger.warning(f"资源格式验证失败: {resource}, 错误: {e}")

        if not resource_objects:
            return message, {}

        # 委托给ResourceProcessor处理
        enhanced_content, resource_info = await self.resource_processor.process_resources(
            message, resource_objects
        )

        # 构建完整的增强消息
        if enhanced_content:
            enhanced_message = f"用户问题：{message}\n\n请基于以下参考信息回答用户问题。如果参考信息不足，请说明并提供您的最佳回答：\n\n{enhanced_content}"
        else:
            enhanced_message = message

        # 转换ResourceInfo为dict格式以保持兼容性
        resource_context = {"resource_info": resource_info.model_dump()} if resource_info else {}

        return enhanced_message, resource_context

    async def _process_runtime_resource(self, runtime_resource: Dict[str, Any]) -> None:
        """
        处理runtime_resource参数

        Args:
            runtime_resource: 运行时资源配置
        """
        self._check_cancellation()

        try:
            from src.api.models.request_models import RuntimeResource

            resource_config = RuntimeResource.model_construct(**runtime_resource)

            logger.info(
                f"处理runtime_resource: type={resource_config.type}, region={resource_config.region}"
            )

            # 根据不同的资源类型进行处理
            if resource_config.type == "agentbay":
                await self._process_agentbay_resource(resource_config)
            elif resource_config.type == "desktop":
                await self._process_desktop_resource(resource_config)
            elif resource_config.type == "none":
                logger.info("runtime_resource类型为none，跳过运行时资源处理")
            else:
                logger.warning(f"未知的runtime_resource类型: {resource_config.type}")

            # 将处理结果存储到context中
            self.context["runtime_resource_processed"] = True
            self.context["runtime_resource_type"] = resource_config.type

        except Exception as e:
            logger.error(f"处理runtime_resource失败: {e}")
            self.context["runtime_resource_error"] = str(e)

    async def _process_agentbay_resource(self, resource_config) -> None:
        """
        处理agentbay类型的runtime_resource

        Args:
            resource_config: RuntimeResource配置对象
        """
        self._check_cancellation()
        # 可以根据token进行agentbay相关的资源配置
        if resource_config.token:
            logger.info(f"处理agentbay资源，token: {resource_config.token[:10]}...")
        else:
            logger.warning("agentbay资源配置缺少token")

    async def _process_desktop_resource(self, resource_config) -> None:
        """
        处理desktop类型的runtime_resource

        Args:
            resource_config: RuntimeResource配置对象
        """
        self._check_cancellation()
        # TODO: 实现desktop资源处理逻辑
        # 这里可以根据cloud_resource_id和region进行桌面资源配置
        logger.info(
            f"处理desktop资源，resource_id: {resource_config.cloud_resource_id}, region: {resource_config.region}, token: {resource_config.token[:10] if resource_config.token else 'None'}..."
        )

    def _build_resource_summary(self, resource_info: Dict[str, Any]) -> str:
        """构建资源处理摘要（用于记录到记忆中）"""

        self._check_cancellation()
        summary_parts = []

        # 处理知识库信息
        knowledge_bases = resource_info.get("knowledge_bases", [])
        if knowledge_bases:
            kb_summaries = []
            for kb in knowledge_bases:
                kb_ids = kb.get("kb_ids", ["unknown"])
                kb_names = ", ".join(kb_ids) if isinstance(kb_ids, list) else str(kb_ids)
                success = kb.get("success", False)
                result_count = kb.get("result_count", 0)
                if success:
                    kb_summaries.append(f"{kb_names}({result_count}个结果)")
                else:
                    error_msg = kb.get("error_message", "未知错误")
                    kb_summaries.append(f"{kb_names}(失败: {error_msg})")
            summary_parts.append(f"知识库检索: {', '.join(kb_summaries)}")

        # 处理文件信息
        files = resource_info.get("files", [])
        if files:
            file_summaries = []
            for file in files:
                filename = file.get("filename", "unknown")
                success = file.get("success", False)
                if success:
                    file_size = file.get("file_size", 0)
                    file_summaries.append(f"{filename}({file_size}字节)")
                else:
                    error_msg = file.get("error_message", "未知错误")
                    file_summaries.append(f"{filename}(失败: {error_msg})")
            summary_parts.append(f"文件处理: {', '.join(file_summaries)}")

        return "资源处理摘要: " + "; ".join(summary_parts) if summary_parts else "未处理任何资源"

    async def cleanup(self) -> None:
        """清理资源"""

        # 清理资源处理器
        if hasattr(self, "resource_processor") and self.resource_processor:
            await self.resource_processor.cleanup()

        # 清理MCP服务器
        if self.server_manager:
            await self.server_manager.cleanup()

        self._initialized = False
